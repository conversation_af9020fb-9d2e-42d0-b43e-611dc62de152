<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600"
     viewBox="0 0 800 600"
     xmlns="http://www.w3.org/2000/svg">

  <!-- Dégradés -->
  <defs>
    <!-- Disque solaire -->
    <radialGradient id="sunGradient" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%"  stop-color="#fffce8"/>
      <stop offset="60%" stop-color="#fff5b4"/>
      <stop offset="100%" stop-color="#ff7e00"/>
    </radialGradient>

    <!-- Halo -->
    <radialGradient id="halo" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%"   stop-color="#fffce8" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="#fffce8" stop-opacity="0"/>
    </radialGradient>

    <!-- Flare horizontal -->
    <linearGradient id="flareGrad" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%"   stop-color="#fffce8" stop-opacity="0"/>
      <stop offset="45%"  stop-color="#fffce8" stop-opacity="0.75"/>
      <stop offset="55%"  stop-color="#fffce8" stop-opacity="0.75"/>
      <stop offset="100%" stop-color="#fffce8" stop-opacity="0"/>
    </linearGradient>

    <!-- Flare vertical -->
    <linearGradient id="flareGradV" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%"   stop-color="#fffce8" stop-opacity="0"/>
      <stop offset="45%"  stop-color="#fffce8" stop-opacity="0.6"/>
      <stop offset="55%"  stop-color="#fffce8" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="#fffce8" stop-opacity="0"/>
    </linearGradient>
  </defs>

  <!-- Halo diffus autour du soleil -->
  <circle cx="400" cy="300" r="180" fill="url(#halo)" />

  <!-- Disque solaire -->
  <circle cx="400" cy="300" r="70" fill="url(#sunGradient)" />

  <!-- Flare horizontal -->
  <rect x="0"   y="290" width="800" height="20" fill="url(#flareGrad)" />

  <!-- Flare vertical -->
  <rect x="390" y="0"   width="20"  height="600" fill="url(#flareGradV)" />

  <!-- Hexagones de diffraction (petits) -->
  <g fill="#fffce8" opacity="0.35">
    <polygon points="400,170 415,190 400,210 385,190" />
    <polygon points="400,390 415,410 400,430 385,410" />
    <polygon points="230,300 245,320 230,340 215,320" />
    <polygon points="570,300 585,320 570,340 555,320" />
  </g>
</svg>
